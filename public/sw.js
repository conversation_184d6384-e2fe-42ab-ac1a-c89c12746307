// Service Worker for TrackerHub
// 缓存策略：缓存静态资源，提升性能

const CACHE_NAME = 'trackerhub-v2';
const STATIC_CACHE = [
  // 移除了对根路径'/'的预缓存，避免首次访问重复请求首页
  '/images/album-placeholder.svg',
  '/images/artists/placeholder.svg',
  '/favicon.svg',
  '/site.webmanifest'
];

// 安装事件 - 预缓存关键资源
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        return cache.addAll(STATIC_CACHE);
      })
      .then(() => {
        return self.skipWaiting();
      })
  );
});

// 激活事件 - 清理旧缓存
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      return self.clients.claim();
    })
  );
});

// 拦截请求 - 缓存策略
self.addEventListener('fetch', event => {
  // 只处理GET请求
  if (event.request.method !== 'GET') return;

  // 跳过非HTTP(S)请求
  if (!event.request.url.startsWith('http')) return;

  // 跳过外部请求（如Google Analytics等）
  if (!event.request.url.includes(self.location.origin)) return;

  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // 如果缓存中有，直接返回
        if (response) {
          return response;
        }

        // 否则从网络获取
        return fetch(event.request).then(response => {
          // 检查是否是有效响应
          if (!response || response.status !== 200 || response.type !== 'basic') {
            return response;
          }

          // 克隆响应用于缓存
          const responseToCache = response.clone();

          // 缓存静态资源
          if (shouldCache(event.request.url)) {
            caches.open(CACHE_NAME)
              .then(cache => {
                cache.put(event.request, responseToCache);
              });
          }

          return response;
        }).catch(error => {
          console.warn('Fetch failed for:', event.request.url, error);
          // 返回一个基本的错误响应而不是让Promise被拒绝
          return new Response('Network error', { status: 408, statusText: 'Request Timeout' });
        });
      })
  );
});

// 判断是否应该缓存
function shouldCache(url) {
  // 缓存图片、CSS、JS等静态资源
  return url.includes('/images/') || 
         url.includes('/css/') || 
         url.includes('/js/') ||
         url.endsWith('.svg') ||
         url.endsWith('.png') ||
         url.endsWith('.jpg') ||
         url.endsWith('.jpeg') ||
         url.endsWith('.webp');
}
