---
/**
 * MobileAd.astro - 移动端专用广告组件
 * 
 * 这个组件只在移动设备上显示广告，使用网站的设备检测逻辑
 * 可以放置在任何页面中，只有在移动设备上才会显示
 */

interface Props {
  class?: string;
  adType?: 'native' | 'social';
}

const { 
  class: className = "",
  adType = "native" // 默认使用原生广告
} = Astro.props;
---

<!-- 移动端广告容器 -->
<div class={`th-mobile-view mobile-ad-container ${className}`}>
  {adType === 'native' && (
    <>
      <!-- Adsterra Native Banner Ad (移动端) -->
      <script 
        async="async" 
        data-cfasync="false" 
        src="//pl27396561.profitableratecpm.com/f933a7242cf1fd4246d1da49df9e0f0e/invoke.js"
      ></script>
      <div id="container-f933a7242cf1fd4246d1da49df9e0f0e"></div>
    </>
  )}
  
  {adType === 'social' && (
    <!-- Adsterra Social Bar Ad (移动端) -->
    <script type='text/javascript' src='//pl27396542.profitableratecpm.com/42/6d/e4/426de431172e88a367692eba324decf1.js'></script>
  )}
</div>

<!-- 桌面端广告容器（不显示） -->
<div class="th-desktop-view hidden"></div>

<style>
  .mobile-ad-container {
    width: 100%;
    margin: 0.5rem 0;
    min-height: 200px; /* 减少广告预留空间 */
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
