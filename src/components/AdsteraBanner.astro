---
/**
 * AdsteraBanner.astro - Adsterra原生广告横幅组件
 * 
 * 使用方法：
 * 在需要显示广告的页面中导入并使用此组件
 * import AdsteraBanner from '../components/AdsteraBanner.astro';
 * 
 * <AdsteraBanner />
 */

interface Props {
  class?: string;
}

const { class: className = "" } = Astro.props;
---

<div class={`adstera-banner-container ${className}`}>
  <script 
    async="async" 
    data-cfasync="false" 
    src="//pl27396561.profitableratecpm.com/f933a7242cf1fd4246d1da49df9e0f0e/invoke.js"
  ></script>
  <div id="container-f933a7242cf1fd4246d1da49df9e0f0e"></div>
</div>

<style>
  .adstera-banner-container {
    width: 100%;
    margin: 1rem 0;
    min-height: 280px; /* 为广告预留空间，避免布局偏移 */
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
