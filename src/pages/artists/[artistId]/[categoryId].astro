---
import MainLayout from '../../../layouts/MainLayout.astro';
import artistsData from '../../../data/artists.json';
import { promises as fs } from 'fs';
import path from 'path';
import SmartImage from '../../../components/SmartImage.astro';
import { t } from '../../../i18n';
import SimpleSubmissionForm from '../../../components/content-submission/SimpleSubmissionForm.astro';
import { ENABLE_SUBMISSION_FORM } from '../../../config/featureFlags';

// 设置英语环境
const lang = 'en';

// 启用预渲染
export const prerender = true;

export async function getStaticPaths() {
  const paths = [];

  for (const artist of artistsData.artists) {
    for (const category of artist.categories) {
      // 只为有数据的分类生成路径
      if (category.hasData !== false) {
        paths.push({
          params: {
            artistId: artist.id,
            categoryId: category.id
          },
          props: {
            artist,
            category
          }
        });
      }
    }
  }

  return paths;
}

const { artistId, categoryId } = Astro.params;
const { artist, category } = Astro.props;

// 确保 artist 和 category 对象都存在
if (!artist) {
  throw new Error(`Artist with ID ${artistId} not found`);
}

if (!category) {
  throw new Error(`Category with ID ${categoryId} not found for artist ${artist.name}`);
}

// 面包屑导航数据
const breadcrumbs = [
  { name: t('Home', lang), url: '/' },
  { name: t('Artists', lang), url: '/artists' },
  { name: artist.name, url: `/artists/${artist.id}` },
  { name: t(`categories.${category.id}`, lang) || category.name, url: `/artists/${artist.id}/${category.id}`, current: true }
];

// 从JSON文件加载分类下的专辑数据
let albums = [];

try {
  // 只有 unreleased 分类才尝试加载 eras.json 文件
  if (categoryId === 'unreleased') {
    const categoryErasPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras.json');

    try {
      // 读取专辑列表
      const erasData = await fs.readFile(categoryErasPath, 'utf-8');
      albums = JSON.parse(erasData);

      // 确保每个专辑都有coverImage和backgroundColor字段
      albums = albums.map(album => ({
        ...album,
        coverImage: album.coverImage || '/images/eras/default.svg',
        backgroundColor: album.backgroundColor || '#333'
      }));

    } catch (erasError) {
      console.error(`Eras data not found for category ${categoryId}:`, erasError);
    }
  }
} catch (error) {
  console.error(`Error loading category data for ${categoryId}:`, error);
}

// 如果没有找到数据，使用默认模拟数据
if (albums.length === 0) {
  albums = [
    {
      id: 'default-album',
      name: 'Default Album',
      description: 'No album data available',
      trackCount: 0,
      backgroundColor: '#333',
      coverImage: '/images/eras/default.svg',
      artist: artist.name,
      artistId: artist.id
    }
  ];
}
---

<MainLayout title={`${category.name} - ${artist.name} - TrackerHive`}>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbs.map((breadcrumb, index) => (
          <li class="inline-flex items-center">
            {index > 0 && (
              <svg class="w-3 h-3 mx-1 text-text-secondary" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
              </svg>
            )}
            <a 
              href={breadcrumb.url} 
              class={`inline-flex items-center text-sm font-medium ${
                breadcrumb.current 
                  ? 'text-primary cursor-default' 
                  : 'text-text-secondary hover:text-primary'
              }`}
              aria-current={breadcrumb.current ? 'page' : undefined}
            >
              {breadcrumb.name}
            </a>
          </li>
        ))}
      </ol>
    </nav>
    
    <!-- 分类头部 -->
    <div class="mb-10 rounded-xl overflow-hidden shadow-lg bg-dark-elevated">
      <div class="px-8 py-12 md:py-16 flex flex-col md:flex-row items-start gap-8">
        <div class="md:max-w-3xl flex-grow">
          <h1 class="text-3xl md:text-4xl font-bold mb-4 text-white">{t(`categories.${category.id}`, lang) || category.name}</h1>
          <p class="text-base md:text-lg text-text-secondary">
            {t(`categories.${category.id}.description`, lang) || category.description}
          </p>
        </div>
        <div class="w-full md:w-64 flex-shrink-0">
          <div class="aspect-square bg-dark rounded-lg overflow-hidden">
            {artist.image ? (
              <img 
                src={artist.image} 
                alt={artist.name} 
                class="w-full h-full object-cover" 
                onerror="this.onerror=null; this.src='/images/artists/placeholder.svg';"
              />
            ) : (
              <div class="w-full h-full flex items-center justify-center text-text-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M5.52 19.346a7.704 7.704 0 0 0 6.48 1.154 7.706 7.706 0 0 0 6.48-1.154"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    
    <!-- 专辑时期列表 -->
    <section>
      <h2 class="text-2xl font-bold text-white mb-6">Album Eras ({albums.length})</h2>
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
        {albums.map((era) => (
          <a href={`/artists/${artist.id}/${categoryId}/eras/${era.id}`} class="group">
            <div 
              class="relative overflow-hidden rounded-xl shadow-md hover:shadow-lg transition-all border border-dark-hover hover:border-primary/50 bg-black"
            >
              <div class="aspect-square">
                <SmartImage
                  src={era.coverImage}
                  alt={era.name}
                  className="w-full h-full object-cover"
                  artistId={artist.id}
                  categoryId={categoryId}
                  eraId={era.id}
                  placeholderSrc="/images/eras/album-placeholder.svg"
                />
              </div>
              <div class="p-2 sm:p-3 md:p-4 bg-gradient-to-t from-black/80 to-transparent">
                <h3 class="font-semibold text-sm sm:text-base md:text-lg text-white mb-0.5 md:mb-1 truncate">{era.name}</h3>
                <p class="text-xs sm:text-sm text-text-secondary mb-1 md:mb-2 line-clamp-1">{era.description || ''}</p>
                <div class="flex items-center text-xs text-text-secondary">
                  <span>{era.trackCount || 0} tracks</span>
                </div>
              </div>
            </div>
          </a>
        ))}
      </div>
    </section>

    <!-- Add Track Section (feature-flagged) -->
    {ENABLE_SUBMISSION_FORM && (
      <section class="mt-12">
        <SimpleSubmissionForm artistId={artist.id} categoryId={category.id} />
      </section>
    )}
  </main>
</MainLayout>
