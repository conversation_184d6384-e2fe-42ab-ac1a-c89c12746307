---
import MainLayout from '../../../../../../layouts/MainLayout.astro';
import SimpleSubmissionForm from '../../../../../../components/content-submission/SimpleSubmissionForm.astro';
import { ENABLE_SUBMISSION_FORM } from '../../../../../../config/featureFlags';
import TrackDownloadButton from '../../../../../../components/TrackDownloadButton.astro';
import { promises as fs } from 'fs';
import path from 'path';
import { t } from '../../../../../../i18n';

// Set Portuguese environment
const lang = 'pt';

// 启用预渲染
export const prerender = true;

// 获取静态路径
export async function getStaticPaths() {
  try {
    const categoriesDir = path.join(process.cwd(), 'public', 'data', 'artists', 'playboi-carti', 'categories');
    const categories = await fs.readdir(categoriesDir);

    const paths = [];

    for (const category of categories) {
      // 跳过非目录项
      const categoryPath = path.join(categoriesDir, category);
      const categoryStats = await fs.stat(categoryPath);
      if (!categoryStats.isDirectory()) continue;

      // 读取分类信息
      const categoryInfoPath = path.join(categoryPath, 'info.json');
      let categoryData = { name: category };
      try {
        const categoryInfo = await fs.readFile(categoryInfoPath, 'utf-8');
        categoryData = JSON.parse(categoryInfo);
      } catch (error) {
        console.error(`Error reading category info for ${category}:`, error);
      }

      // 读取分类信息中的显示模式
      let displayMode = 'albums'; // 默认为 albums 模式

      if (categoryData && categoryData.displayMode) {
        displayMode = categoryData.displayMode;
      }

      // 只有在 albums 模式下才尝试读取 eras 目录
      if (displayMode === 'albums') {
        const erasDir = path.join(categoryPath, 'eras');

        try {
          // 先检查目录是否存在
          const erasDirStats = await fs.stat(erasDir).catch(() => null);

          if (erasDirStats && erasDirStats.isDirectory()) {
            const eras = await fs.readdir(erasDir);

            for (const era of eras) {
              if (!era.endsWith('.json')) continue;

              const eraId = era.replace('.json', '');

              paths.push({
                params: { categoryId: category, eraId },
                props: { categoryData }
              });
            }
          }
        } catch (error) {
          console.error(`Error reading eras for category ${category}:`, error);
        }
      }
    }

    return paths;
  } catch (error) {
    console.error('Error generating static paths:', error);
    return [];
  }
}

// 页面组件
const { categoryId, eraId } = Astro.params;
const { categoryData } = Astro.props;

// 加载分类数据
// 将中文分类名称转换为英文
let categoryName = categoryData?.name || 'Unknown Category';
// 分类名称映射
const categoryNameMap = {
  '未发行': 'Unreleased',
  '专辑': 'Albums',
  '合作': 'Collaborations',
  '单曲': 'Singles'
};

// 如果有映射，使用英文名称
if (categoryNameMap[categoryName]) {
  categoryName = categoryNameMap[categoryName];
}

// 加载专辑详细数据
let eraData = { name: 'Unknown Album', tracks: [] };

try {
  const eraPath = path.join(process.cwd(), 'public', 'data', 'artists', 'playboi-carti', 'categories', categoryId, 'eras', `${eraId}.json`);
  const eraContent = await fs.readFile(eraPath, 'utf-8');
  eraData = JSON.parse(eraContent);

  if (eraData.tracks && eraData.tracks.length > 0) {
    eraData.tracks = eraData.tracks.map((track) => {
      // 处理originalContent字段，如果它是字符串，则尝试解析为JSON对象
      let processedTrack = { ...track };

      if (track.originalContent && typeof track.originalContent === 'string') {
        try {
          processedTrack.originalContent = JSON.parse(track.originalContent);
        } catch (e) {
          console.error(`Error parsing originalContent for track ${track.name}:`, e);
          // 如果解析失败，创建一个简单的对象来存储原始字符串
          processedTrack.originalContent = {
            rawContent: track.originalContent
          };
        }
      }

      return processedTrack;
    });
  }
} catch (error) {
  console.error(`Error loading era data for ${eraId}:`, error);
}

// 构建面包屑导航 - 使用实际的曲目名称
const breadcrumbs = [
  { name: t('common.home', lang), url: '/pt' },
  { name: t('common.artists', lang), url: '/pt/artists' },
  { name: 'Playboi Carti', url: '/pt/artists/playboi-carti' },
  { name: categoryName, url: `/pt/artists/playboi-carti/${categoryId}` },
  { name: eraData.name, url: '#' }
];
---

<MainLayout title={`${eraData.name} - ${categoryName} - Playboi Carti - TrackerHive`} lang={lang}>
  <style>

    /* 标签基本样式 */
    .quality-label, .generic-label, .available-label {
      display: inline-flex;
      align-items: center;
      padding: 0.25rem 0.5rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 500;
      margin-right: 0.25rem;
      white-space: nowrap;
    }

    /* Quality label colors */
    .quality-label {
      background-color: rgba(75, 85, 99, 0.5);
      color: rgb(209, 213, 219);
      border: 1px solid rgba(75, 85, 99, 0.8);
    }

    /* 预定义标签样式类 */
    .high-quality-label {
      background-color: rgba(16, 185, 129, 0.2) !important;
      color: rgb(110, 231, 183) !important;
      border: 1px solid rgba(16, 185, 129, 0.5) !important;
    }

    .low-quality-label {
      background-color: rgba(239, 68, 68, 0.2) !important;
      color: rgb(252, 165, 165) !important;
      border: 1px solid rgba(239, 68, 68, 0.5) !important;
    }

    .cd-quality-label {
      background-color: rgba(139, 92, 246, 0.2) !important;
      color: rgb(196, 181, 253) !important;
      border: 1px solid rgba(139, 92, 246, 0.5) !important;
    }

    .recording-label {
      background-color: rgba(245, 158, 11, 0.2) !important;
      color: rgb(253, 230, 138) !important;
      border: 1px solid rgba(245, 158, 11, 0.5) !important;
    }

    .snippet-label {
      background-color: rgba(245, 158, 11, 0.2) !important;
      color: rgb(253, 230, 138) !important;
      border: 1px solid rgba(245, 158, 11, 0.5) !important;
    }

    .available-label {
      background-color: rgba(59, 130, 246, 0.2) !important;
      color: rgb(147, 197, 253) !important;
      border: 1px solid rgba(59, 130, 246, 0.5) !important;
    }

    /* OG File 标签特殊样式 */
    .generic-label:not(.cd-quality-label):not(.snippet-label):not(.recording-label):not(.high-quality-label):not(.low-quality-label):not(.available-label) {
      background-color: rgba(139, 92, 246, 0.2) !important;
      color: rgb(196, 181, 253) !important;
      border: 1px solid rgba(139, 92, 246, 0.5) !important;
    }

    /* 播放按钮样式 */
    .play-button {
      transition: all 0.2s ease;
    }

    .play-button.loading {
      opacity: 0.7;
    }

    .play-button.playing {
      background-color: #1DB954;
    }

    .play-button:hover {
      transform: scale(1.05);
    }

    /* 移动端notes按钮样式 */
    .mobile-notes-button {
      z-index: 10;
    }
  </style>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbs.map((breadcrumb, index) => (
            <li class="inline-flex items-center">
              {index > 0 ? (
                  <svg class="w-3 h-3 mx-1 text-text-secondary" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                  </svg>
              ) : null}
              <a
                  href={breadcrumb.url}
                  class={`inline-flex items-center text-sm font-medium ${
                      index === breadcrumbs.length - 1
                          ? 'text-text-primary cursor-default'
                          : 'text-text-secondary hover:text-text-primary'
                  }`}
              >
                {breadcrumb.name}
              </a>
            </li>
        ))}
      </ol>
    </nav>

    <!-- 专辑信息 -->
    <div class="bg-dark rounded-xl overflow-hidden shadow-md mb-8">
      <div class="md:flex">
        <div class="md:w-1/3 lg:w-1/4 p-6 flex justify-center items-center bg-black/20">
          {eraData.coverImage ? (
              <img
                  id="album-cover"
                  src={eraData.coverImage}
                  alt={`${eraData.name} Cover`}
                  class="w-full max-w-[300px] h-auto rounded-lg shadow-lg"
              />
          ) : (
              <div class="w-full aspect-square max-w-[300px] bg-gray-800 rounded-lg flex items-center justify-center">
                <span class="text-text-secondary">No Cover</span>
              </div>
          )}
        </div>
        <div class="p-8 md:p-10 md:w-2/3 lg:w-3/4 flex flex-col justify-center">
          <h1 class="text-3xl md:text-4xl font-bold mb-4 text-white">{eraData.name}</h1>
          {eraData.description ? (
              <p class="text-base md:text-lg text-text-secondary mb-6">{eraData.description}</p>
          ) : null}
          <div class="flex flex-wrap gap-4 text-sm text-text-secondary">
            <div class="bg-black/30 px-3 py-1 rounded-full">
              {eraData.tracks?.length || 0} tracks
            </div>
            {eraData.artist ? (
                <div class="bg-black/30 px-3 py-1 rounded-full">
                  Artist: {eraData.artist}
                </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>

    <!-- 曲目列表 -->
    <div class="mb-8">
      <h2 class="text-2xl font-bold text-white mb-6">Tracks ({eraData.tracks?.length || 0})</h2>

      <div class="bg-dark rounded-xl overflow-hidden shadow-md">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-800">
            <thead class="bg-black/20">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                Track
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                Length
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                Quality/Available
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-text-secondary uppercase tracking-wider">
                Download
              </th>
            </tr>
            </thead>
            <tbody id="tracks-container" class="divide-y divide-gray-800">
            {/* 初始只渲染前20个曲目，其余的通过JavaScript动态加载 */}
            {eraData.tracks?.slice(0, 20).map((track) => (
                <tr class="hover:bg-black/10 transition-colors">
                  <td class="px-6 py-4">
                    <div class="flex items-center">
                      {/* 根据是否有length显示不同的按钮 */}
                      <div class="mr-3 flex items-center">
                        {track.length ? (
                            <button
                                class="play-button w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white hover:bg-primary-dark transition-colors"
                                title="Play"
                                data-track-name={track.name}
                                data-track-length={track.length}
                                data-track-artists={track.artists?.join(', ')}
                                data-track-audio={track.audioUrl || ''}
                                data-track-original-url={track.originalUrl || ''}
                                data-track-original-content={JSON.stringify(track.originalContent || {})}
                                data-track-index={eraData.tracks.indexOf(track)}
                            >
                              {/* 播放图标 */}
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 play-icon" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                              </svg>
                              {/* 加载图标 */}
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 loading-icon hidden animate-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M12 6v6l4 2"></path>
                              </svg>
                            </button>
                        ) : track.originalUrl ? (
                            <a href={track.originalUrl} target="_blank" rel="noopener noreferrer" class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors" title="Go to source">
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                              </svg>
                            </a>
                        ) : (
                            <div class="w-8 h-8"></div>
                        )}
                      </div>
                      <div class="track-content relative">
                        <div class="flex items-center gap-2">
                          <span class="text-white">{track.name}</span>
                          {/* 别名信息 */}
                          {track.aliases && track.aliases.length > 0 ? (
                              <span class="text-yellow-400 text-xs font-medium bg-gray-800/70 px-2 py-0.5 rounded border border-gray-700">
                              {Array.isArray(track.aliases) ? track.aliases.join(', ') : track.aliases}
                            </span>
                          ) : track.originalContent?.aliases && track.originalContent.aliases.length > 0 ? (
                              <span class="text-yellow-400 text-xs font-medium bg-gray-800/70 px-2 py-0.5 rounded border border-gray-700">
                              {Array.isArray(track.originalContent.aliases) ? track.originalContent.aliases.join(', ') : track.originalContent.aliases}
                            </span>
                          ) : null}
                        </div>
                        {/* 主要艺术家信息 */}
                        {track.artists && track.artists.length > 0 ? (
                            <p class="text-text-secondary text-sm mt-1">
                              {track.artists.join(', ')}
                            </p>
                        ) : null}

                        {/* 显示原始内容中的艺术家 - 作为字符串处理 */}
                        {track.originalContent && track.originalContent.artists && typeof track.originalContent.artists === 'string' && (
                            <p class="text-text-secondary text-sm mt-1">
                              {track.originalContent.artists}
                            </p>
                        )}

                        {/* 如果originalContent.artists是数组，则使用join方法 */}
                        {track.originalContent && track.originalContent.artists && Array.isArray(track.originalContent.artists) && (
                            <p class="text-text-secondary text-sm mt-1">
                              {track.originalContent.artists.join(', ')}
                            </p>
                        )}

                        {/* 添加notes信息，桌面端悬浮显示，移动端不显示 */}
                        {track.notes && (
                            <>
                              <button class="notes-info-button hidden md:block absolute top-0 right-0 opacity-0 pointer-events-none">
                                <span class="sr-only">Track Notes</span>
                              </button>
                              <div class="hidden">{track.notes}</div>
                            </>
                        )}
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <span class="track-length text-text-secondary" data-length={track.length}>
                      {track.length && /^\d+:\d{2}$/.test(track.length) ? track.length : ''}
                    </span>
                  </td>
                  <td class="px-6 py-4">
                    <div class="flex flex-wrap gap-1">
                      {/* 显示Quality和Available作为标签 */}
                      {track.quality ? (
                          <span class="quality-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                          {track.quality}
                        </span>
                      ) : null}
                      {track.availableLength ? (
                          <span class="available-label inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                          {track.availableLength}
                        </span>
                      ) : null}
                      {/* 如果有labels字段，也显示为标签 */}
                      {track.labels ? track.labels.map((label) => {
                        let labelClass = "generic-label";
                        const lowerLabel = label.toLowerCase();

                        // 根据标签内容确定类名
                        if (lowerLabel.includes('high') || label === 'High Quality') {
                          labelClass = "quality-label high-quality-label";
                        } else if (lowerLabel.includes('low') || label === 'Low Quality') {
                          labelClass = "quality-label low-quality-label";
                        } else if (lowerLabel === 'full') {
                          labelClass = "available-label";
                        } else if (lowerLabel === 'og file' || label === 'OG File') {
                          labelClass = "og-file-label";
                        }

                        return (
                            <span class={`${labelClass} inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium`} data-label-text={label}>
                            {label}
                          </span>
                        );
                      }) : null}
                    </div>
                  </td>
                  <td class="px-6 py-4 text-right">
                    {track.length ? (
                      <TrackDownloadButton
                        track={{
                          id: track.id || `era_${track.name}`,
                          title: track.name,
                          url: track.audioUrl,
                          originalUrl: track.originalUrl,
                          originalContent: track.originalContent,
                          size: track.size
                        }}
                        className="track-download"
                      />
                    ) : (
                      <span class="text-gray-500 text-sm">-</span>
                    )}
                  </td>
                </tr>
            ))}

            {(!eraData.tracks || eraData.tracks.length === 0) ? (
                <tr>
                  <td colspan="4" class="px-6 py-8 text-center text-text-secondary">
                    No tracks available for this album.
                  </td>
                </tr>
            ) : null}
            </tbody>
          </table>
        </div>
        {/* 只有当曲目数量超过20个时才显示"加载更多"按钮 */}
        {eraData.tracks && eraData.tracks.length > 20 && (
          <div id="load-more-container" class="py-4 px-6 bg-dark-elevated border-t border-gray-800 text-center">
            <button 
              id="load-more-button" 
              class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              data-current-page="1"
              data-total-tracks={eraData.tracks.length}
            >
              Load More
            </button>
          </div>
        )}
      </div>
    </div>

    <!-- Add Track Section (feature-flagged) -->
    {ENABLE_SUBMISSION_FORM && (
      <SimpleSubmissionForm artistId="playboi-carti" categoryId={categoryId} eraId={eraId} hideHeader={true} />
    )}

  </main>


</MainLayout>

<script define:vars={{ tracksData: eraData.tracks || [] }}>
  // 将eraData.tracks数据保存到window对象中，供客户端脚本使用
  window.tracksData = tracksData;
</script>

<script>
  // 初始化播放功能
  document.addEventListener('DOMContentLoaded', () => {
    // 初始化notes提示功能
    initNotesTooltips();

    // 初始化质量标签样式
    initQualityLabels();

    // 获取所有播放按钮
    const playButtons = document.querySelectorAll('.play-button');

    // 重置所有按钮状态的函数
    function resetAllButtonsState() {
      playButtons.forEach(btn => {
        // 保持按钮的原始样式，只替换内部图标
        btn.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 play-icon" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
          </svg>
        `;
        btn.classList.remove('loading', 'playing');
      });
    }

    // 为每个播放按钮添加点击事件
    playButtons.forEach((button) => {
      button.addEventListener('click', () => {
        // 重置所有按钮状态
        resetAllButtonsState();

        // 显示当前按钮的加载状态
        button.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 animate-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M12 6v6l4 2"></path>
          </svg>
        `;
        button.classList.add('loading');

        // 记录当前播放按钮
        const currentPlayingButton = button;

        // 获取曲目名称和艺术家信息
        const trackName = button.getAttribute('data-track-name');
        const trackArtistsRaw = button.getAttribute('data-track-artists');

        // 获取originalContent数据
        const trackOriginalContentStr = button.getAttribute('data-track-original-content');
        let parsedOriginalContent = {};
        let originalContentObj = {};

        try {
          if (trackOriginalContentStr) {
            // 先尝试直接解析
            try {
              parsedOriginalContent = JSON.parse(trackOriginalContentStr);
            } catch (parseError) {
              console.error('First-level parsing error:', parseError);
              
              // 如果直接解析失败，可能是因为字符串已经被转义了
              // 尝试对内容进行二次解析
              try {
                // 将转义字符替换回来，修复可能的双重转义问题
                const fixedStr = trackOriginalContentStr
                  .replace(/\\\\(["'])/g, '\\$1') // 修复双重转义
                  .replace(/\\([^"'\\])/g, '$1'); // 移除不必要的转义
                
                originalContentObj = JSON.parse(fixedStr);
                
                // 如果内容是字符串，可能需要再次解析
                if (typeof originalContentObj === 'string') {
                  originalContentObj = JSON.parse(originalContentObj);
                }
              } catch (secondError) {
                console.error('Second-level parsing error:', secondError);
              }
            }
          }
        } catch (e) {
          console.error('Error handling originalContent:', e);
        }

        // 获取音频URL
        // 尝试从多个可能的来源获取URL
        let trackAudioUrl = parsedOriginalContent.url || 
                          (originalContentObj && originalContentObj.url) || 
                          button.getAttribute('data-track-audio');
        
        // 如果还是没有找到URL，尝试使用originalUrl
        if (!trackAudioUrl) {
          // 直接使用originalUrl作为备选音频源
          trackAudioUrl = button.getAttribute('data-track-original-url');
          console.log('Using originalUrl as fallback:', trackAudioUrl);
        }
        


        if (trackAudioUrl) {
          // 获取专辑名称
          const albumTitle = document.querySelector('.album-title')?.textContent || '';

          // 获取曲目索引
          const trackIndex = parseInt(button.getAttribute('data-track-index') || '0');

          // 获取 originalContent 中的艺术家信息
          const artistInfo = parsedOriginalContent.artists || trackArtistsRaw || '';

          // 创建曲目对象，确保包含必要字段
          const track = {
            name: trackName,
            title: trackName,
            artists: trackArtistsRaw ? trackArtistsRaw.split(', ') : [],
            url: trackAudioUrl, // 直接使用originalContent.url
            audioUrl: trackAudioUrl, // 保持url和audioUrl一致
            albumCover: document.querySelector('#album-cover')?.getAttribute('src') || '',
            album: albumTitle,
            artist: artistInfo,
            id: `track-${trackName}-${Date.now()}`
          };

          // 直接调用全局的 playTrack 函数，确保艺术家信息能够正确保存到播放历史记录中
          if (window.playTrack && typeof window.playTrack === 'function') {
            console.log('Using window.playTrack function with artist info:', artistInfo);
            // 使用window.tracksData而不是eraData.tracks
            window.playTrack(track, window.tracksData || [], trackIndex);
          } else {
            // 如果全局函数不可用，使用自定义事件
            const playEvent = new CustomEvent('play-track', {
              detail: {
                track,
                playlist: window.tracksData || [],
                index: trackIndex
              }
            });

            document.dispatchEvent(playEvent);
          }

          // 监听音频加载完成事件，恢复按钮状态
          document.addEventListener('audio-loaded', function resetButtonState() {
            // 只处理当前播放按钮的状态
            if (currentPlayingButton === button) {
              button.classList.remove('loading');
              button.classList.add('playing');
              button.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                  <path fill-rule="evenodd" d="M6.75 5.25a.75.75 0 01.75-.75H9a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H7.5a.75.75 0 01-.75-.75V5.25zm7.5 0A.75.75 0 0115 4.5h1.5a.75.75 0 01.75.75v13.5a.75.75 0 01-.75.75H15a.75.75 0 01-.75-.75V5.25z" clip-rule="evenodd" />
                </svg>
              `;
            }
            // 移除事件监听器，避免重复执行
            document.removeEventListener('audio-loaded', resetButtonState);
          });

          // 监听音频加载失败事件，恢复按钮状态
          document.addEventListener('audio-error', function resetButtonState() {
            // 只处理当前播放按钮的状态
            if (currentPlayingButton === button) {
              button.classList.remove('loading');
              resetAllButtonsState();
            }
            // 移除事件监听器，避免重复执行
            document.removeEventListener('audio-error', resetButtonState);
          });
        }
      });
    });
  });

  // 初始化notes提示功能
  function initNotesTooltips() {
    // 为桌面端添加鼠标悬停效果
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
      // 检查这一行是否有notes数据
      const notesButton = row.querySelector('.notes-info-button');
      if (notesButton) {
        const notesContent = notesButton.nextElementSibling.textContent.trim();

        if (notesContent) {
          // 创建悬浮提示
          const tooltip = document.createElement('div');
          tooltip.className = 'desktop-tooltip fixed bg-black/90 p-3 rounded-md shadow-lg text-sm text-white z-20 max-w-md hidden';
          tooltip.textContent = notesContent;
          document.body.appendChild(tooltip);

          // 鼠标悬停显示
          const trackContent = row.querySelector('.track-content');
          if (trackContent) {
            trackContent.addEventListener('mouseenter', (e) => {
              if (window.innerWidth >= 640) { // 只在桌面端显示
                tooltip.classList.remove('hidden');
                positionTooltip(e, tooltip);
              }
            });

            // 鼠标移动更新位置
            trackContent.addEventListener('mousemove', (e) => {
              if (window.innerWidth >= 640) {
                positionTooltip(e, tooltip);
              }
            });

            // 鼠标离开隐藏
            trackContent.addEventListener('mouseleave', () => {
              tooltip.classList.add('hidden');
            });
          }
        }
      }
    });
  }

  // 定位提示框 - 跟随鼠标位置
  function positionTooltip(e, tooltip) {
    const x = e.clientX + 15;
    const y = e.clientY + 10;

    // 确保tooltip不会超出屏幕
    const rightEdge = x + tooltip.offsetWidth;
    const bottomEdge = y + tooltip.offsetHeight;

    if (rightEdge > window.innerWidth) {
      tooltip.style.left = (window.innerWidth - tooltip.offsetWidth - 10) + 'px';
    } else {
      tooltip.style.left = x + 'px';
    }

    if (bottomEdge > window.innerHeight) {
      tooltip.style.top = (window.innerHeight - tooltip.offsetHeight - 10) + 'px';
    } else {
      tooltip.style.top = y + 'px';
    }
  }
  
  // 将positionTooltip函数添加到全局window对象
  window.positionTooltip = positionTooltip;

  // 初始化质量标签样式 - 用于初始加载的曲目
  function initQualityLabels() {
    // 获取所有曲目行
    const rows = document.querySelectorAll('tbody tr');
    setupQualityLabelsForRows(rows);
  }
  
  // 仅为新添加的曲目设置标签样式
  function setupQualityLabelsForNewRows(newRows) {
    setupQualityLabelsForRows(newRows);
  }
  
  // 为指定的行设置标签样式
  function setupQualityLabelsForRows(rows) {
    if (!rows || rows.length === 0) return;
    
    // 遍历每一行
    rows.forEach(row => {
      // 处理标签颜色
      const qualityLabels = row.querySelectorAll('.quality-label');
      qualityLabels.forEach(label => {
        // 添加基本样式类
        label.classList.add('inline-flex', 'items-center', 'px-2.5', 'py-0.5', 'rounded-full', 'text-xs', 'font-medium');
        
        const text = label.textContent.trim();
        const lowerText = text.toLowerCase();
        
        // 根据标签内容确定类名
        if (lowerText.includes('high') || text === 'High Quality') {
          // 移除其他颜色类
          label.classList.remove('bg-indigo-600/30', 'text-indigo-200');
          // 添加高质量标签样式
          label.classList.add('high-quality-label');
          label.classList.add('bg-green-700', 'text-green-100');
        } else if (lowerText.includes('low') || text === 'Low Quality') {
          // 移除其他颜色类
          label.classList.remove('bg-indigo-600/30', 'text-indigo-200');
          // 添加低质量标签样式
          label.classList.add('low-quality-label');
          label.classList.add('bg-red-700', 'text-red-100');
        }
      });
      
      // 处理可用长度标签
      const availableLabels = row.querySelectorAll('.available-label');
      availableLabels.forEach(label => {
        // 添加基本样式类
        label.classList.add('inline-flex', 'items-center', 'px-2.5', 'py-0.5', 'rounded-full', 'text-xs', 'font-medium');
        
        const text = label.textContent.trim();
        const lowerText = text.toLowerCase();
        
        if (lowerText === 'full') {
          // 移除其他颜色类
          label.classList.remove('bg-indigo-600/30', 'text-indigo-200');
          // 添加Full标签样式
          label.classList.add('bg-blue-700', 'text-blue-100');
        }
      });
      
      // 处理原始文件标签
      const ogFileLabels = row.querySelectorAll('.og-file-label');
      ogFileLabels.forEach(label => {
        // 添加基本样式类
        label.classList.add('inline-flex', 'items-center', 'px-2.5', 'py-0.5', 'rounded-full', 'text-xs', 'font-medium');
        
        const text = label.textContent.trim();
        const lowerText = text.toLowerCase();
        
        if (lowerText === 'og file' || text === 'OG File') {
          // 移除其他颜色类
          label.classList.remove('bg-indigo-600/30', 'text-indigo-200');
          // 添加OG File标签样式
          label.classList.add('bg-purple-700', 'text-purple-100');
        }
      });
      
      // 处理通用标签
      const genericLabels = row.querySelectorAll('.generic-label');
      genericLabels.forEach(label => {
        // 添加基本样式类
        label.classList.add('inline-flex', 'items-center', 'px-2.5', 'py-0.5', 'rounded-full', 'text-xs', 'font-medium');
        
        const text = label.textContent.trim();
        const lowerText = text.toLowerCase();
        
        if (lowerText.includes('snippet')) {
          // 移除其他颜色类
          label.classList.remove('bg-indigo-600/30', 'text-indigo-200');
          // 添加Snippet标签样式
          label.classList.add('snippet-label');
          label.classList.add('bg-yellow-700', 'text-yellow-100');
        } else if (lowerText.includes('beat only')) {
          // 移除其他颜色类
          label.classList.remove('bg-indigo-600/30', 'text-indigo-200');
          // 添加Beat Only标签样式
          label.classList.add('beat-only-label');
          label.classList.add('bg-purple-700', 'text-purple-100');
        } else {
          // 保留默认的柔和紫色
          label.classList.add('bg-indigo-600/30', 'text-indigo-200');
        }
      });
    });
  }
  
  // 为新添加的行设置下载按钮事件监听器
  function setupDownloadButtonsForNewRows(newRows) {
    // 由于使用了事件委托，新添加的下载按钮会自动工作
    // 这个函数保留用于未来可能的扩展
    console.log('Download buttons for new rows are ready (using event delegation)');
  }

  // 将函数添加到全局作用域
  window.setupQualityLabelsForNewRows = setupQualityLabelsForNewRows;
  window.setupQualityLabelsForRows = setupQualityLabelsForRows;
  window.setupDownloadButtonsForNewRows = setupDownloadButtonsForNewRows;
</script>

<script>
  // 全局变量
  let currentPlayingButton = null;
  let originalContentObj = {};

  document.addEventListener('DOMContentLoaded', () => {
    // 获取并存储完整的曲目数据
    try {
      // 尝试从页面上的脚本标签中提取完整的曲目数据
      const scriptTags = document.querySelectorAll('script');
      scriptTags.forEach(script => {
        const content = script.textContent;
        if (content && content.includes('const eraData =')) {
          try {
            // 提取eraData对象
            const match = content.match(/const eraData = ([\s\S]*?);\s*<\/script>/m);
            if (match && match[1]) {
              const eraDataStr = match[1].trim();
              // 将提取的JSON字符串转换为对象
              window.fullEraData = JSON.parse(eraDataStr);
              if (window.fullEraData && window.fullEraData.tracks) {
                window.tracksData = window.fullEraData.tracks;
              }
            }
          } catch (e) {
            console.error('解析eraData失败:', e);
          }
        }
      });
    } catch (e) {
      window.tracksData = [];
    }
    
    // 如果仍然没有数据，创建一个空数组
    if (!window.tracksData) {
      window.tracksData = [];
    }
    
    // 获取所有曲目按钮
    const trackButtons = document.querySelectorAll('.play-button');
    
    // 为每个按钮添加点击事件
    trackButtons.forEach(button => {
      button.addEventListener('click', handleTrackButtonClick);
    });

    // 显示notes信息
    setupNotesInfo();
    
    // 设置加载更多按钮事件
    setupLoadMoreButton();
    
    // 处理时长格式
    formatTrackLengths();
  });
  
  // 设置加载更多按钮
  function setupLoadMoreButton() {
    const loadMoreButton = document.getElementById('load-more-button');
    if (!loadMoreButton) return;
    
    loadMoreButton.addEventListener('click', () => {
      // 获取当前页码和总曲目数
      const currentPage = parseInt(loadMoreButton.getAttribute('data-current-page'));
      const totalTracks = parseInt(loadMoreButton.getAttribute('data-total-tracks'));
      const tracksPerPage = 20;
      
      // 计算下一页的起始和结束索引
      const startIndex = currentPage * tracksPerPage;
      const endIndex = Math.min(startIndex + tracksPerPage, totalTracks);
      
      // 如果已经加载了所有曲目，则隐藏按钮
      if (startIndex >= totalTracks) {
        loadMoreButton.parentElement.style.display = 'none';
        return;
      }
      
      // 显示加载中状态
      loadMoreButton.textContent = 'Loading...';
      loadMoreButton.disabled = true;
      
      try {
        // 使用全局存储的完整曲目数据
        if (!window.tracksData || window.tracksData.length === 0) {
          throw new Error('无法找到完整的曲目数据');
        }
        
        console.log(`加载更多曲目: ${startIndex} 到 ${endIndex}, 总数: ${window.tracksData.length}`);
        
        // 获取当前容器
        const tracksContainer = document.getElementById('tracks-container');
        
        // 获取要添加的曲目数据
        const tracksToAdd = window.tracksData.slice(startIndex, endIndex);
        
        // 创建并添加新的曲目行
        tracksToAdd.forEach((track, index) => {
          // 创建新的行元素
          const newRow = document.createElement('tr');
          newRow.className = 'hover:bg-black/10 transition-colors';
          
          // 计算实际索引（用于数据属性）
          const trackIndex = startIndex + index;
          
          // 设置行内容
          newRow.innerHTML = `
            <td class="px-6 py-4">
              <div class="flex items-center">
                <div class="mr-3 flex items-center">
                  ${track.length ? `
                    <button
                      class="play-button w-8 h-8 flex items-center justify-center rounded-full bg-primary text-white hover:bg-primary-dark transition-colors"
                      title="Play"
                      data-track-name="${track.name || ''}"
                      data-track-length="${track.length || ''}"
                      data-track-artists="${Array.isArray(track.artists) ? track.artists.join(', ') : (track.artists || '')}"
                      data-track-audio="${track.audioUrl || ''}"
                      data-track-original-url="${track.originalUrl || ''}"
                      data-track-original-content='${JSON.stringify(track.originalContent || {}).replace(/'/g, "&apos;")}'                      
                      data-track-index="${trackIndex}"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 play-icon" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd" />
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 loading-icon hidden animate-spin" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <path d="M12 6v6l4 2"></path>
                      </svg>
                    </button>
                  ` : track.originalUrl ? `
                    <a href="${track.originalUrl}" target="_blank" rel="noopener noreferrer" class="w-8 h-8 flex items-center justify-center rounded-full bg-gray-700 text-white hover:bg-gray-600 transition-colors" title="Go to source">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                    </a>
                  ` : `
                    <div class="w-8 h-8"></div>
                  `}
                </div>
                <div class="track-content relative">
                  <div class="flex items-center gap-2">
                    <span class="text-white">${track.name || ''}</span>
                    ${track.aliases && track.aliases.length > 0 ? `
                      <span class="text-yellow-400 text-xs font-medium bg-gray-800/70 px-2 py-0.5 rounded border border-gray-700">
                        ${Array.isArray(track.aliases) ? track.aliases.join(', ') : track.aliases}
                      </span>
                    ` : track.originalContent && track.originalContent.aliases && track.originalContent.aliases.length > 0 ? `
                      <span class="text-yellow-400 text-xs font-medium bg-gray-800/70 px-2 py-0.5 rounded border border-gray-700">
                        ${Array.isArray(track.originalContent.aliases) ? track.originalContent.aliases.join(', ') : track.originalContent.aliases}
                      </span>
                    ` : ''}
                  </div>
                  ${track.artists && track.artists.length > 0 ? `
                    <p class="text-text-secondary text-sm mt-1">
                      ${Array.isArray(track.artists) ? track.artists.join(', ') : track.artists}
                    </p>
                  ` : ''}
                  ${track.originalContent && track.originalContent.artists && typeof track.originalContent.artists === 'string' ? `
                    <p class="text-text-secondary text-sm mt-1">
                      ${track.originalContent.artists}
                    </p>
                  ` : ''}
                  ${track.originalContent && track.originalContent.artists && Array.isArray(track.originalContent.artists) ? `
                    <p class="text-text-secondary text-sm mt-1">
                      ${track.originalContent.artists.join(', ')}
                    </p>
                  ` : ''}
                  ${track.notes ? `
                    <button class="notes-info-button hidden md:block absolute top-0 right-0 opacity-0 pointer-events-none">
                      <span class="sr-only">Track Notes</span>
                    </button>
                    <div class="hidden">${track.notes}</div>
                  ` : ''}
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              <span class="track-length text-text-secondary" data-length="${track.length || ''}">
                ${track.length && /^\d+:\d{2}$/.test(track.length) ? track.length : ''}
              </span>
            </td>
            <td class="px-6 py-4">
              <div class="flex flex-wrap gap-1">
                ${track.quality ? `
                  <span class="quality-label ${track.quality.toLowerCase().replace(/\s+/g, '-')}-label">
                    ${track.quality}
                  </span>
                ` : ''}
                ${track.labels && track.labels.length > 0 ? track.labels.map(label => `
                  <span class="generic-label">
                    ${label}
                  </span>
                `).join('') : ''}
              </div>
            </td>
            <td class="px-6 py-4 text-right">
              ${track.length ? `
                <div class="download-button-container track-download"
                     data-track-id="${track.id || `era_${track.name}`}"
                     data-track-title="${track.name || ''}"
                     data-download-url="${track.audioUrl || track.originalUrl || ''}"
                     data-track-audio="${track.audioUrl || ''}"
                     data-track-original-url="${track.originalUrl || ''}"
                     data-track-original-content='${JSON.stringify(track.originalContent || {}).replace(/'/g, "&apos;")}'>
                  <button class="download-btn" data-action="download" title="Download track">
                    <svg class="download-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                      <polyline points="7,10 12,15 17,10"/>
                      <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                  </button>
                  ${track.size ? `<div class="file-size">${track.size}</div>` : ''}
                </div>
              ` : `
                <span class="text-gray-500 text-sm">-</span>
              `}
            </td>
          `;
          
          // 添加到容器
          tracksContainer.appendChild(newRow);
          
          // 为新添加的播放按钮添加事件监听器
          const playButton = newRow.querySelector('.play-button');
          if (playButton) {
            playButton.addEventListener('click', handleTrackButtonClick);
          }
        });
        
        // 更新页码
        loadMoreButton.setAttribute('data-current-page', (currentPage + 1).toString());
        
        // 如果已加载所有曲目，隐藏按钮
        if (endIndex >= totalTracks) {
          loadMoreButton.parentElement.style.display = 'none';
        } else {
          // 恢复按钮状态
          loadMoreButton.textContent = 'Load More';
          loadMoreButton.disabled = false;
        }
        
        // 获取新添加的曲目行
        const newRows = Array.from(tracksContainer.querySelectorAll('tr')).slice(-tracksToAdd.length);

        // 仅为新添加的曲目设置notes信息
        setupNotesInfoForNewRows(newRows);

        // 为新添加的曲目设置标签样式
        setupQualityLabelsForNewRows(newRows);

        // 格式化新添加的曲目时长
        formatTrackLengths();

        // 为新添加的下载按钮绑定事件监听器
        setupDownloadButtonsForNewRows(newRows);
      } catch (error) {
        console.error('加载更多曲目时出错:', error);
        loadMoreButton.textContent = 'Error loading tracks. Try again';
        loadMoreButton.disabled = false;
      }
    });
  }

  // 处理曲目按钮点击
  function handleTrackButtonClick(event) {
    event.preventDefault();
    event.stopPropagation();
    
    // 获取实际的按钮元素（可能是SVG或其子元素）
    const button = event.currentTarget;
    
    // 设置当前播放按钮
    currentPlayingButton = button;
    
    // 显示加载状态
    button.classList.add('loading');
    button.classList.remove('playing');
    
    // 更换图标为加载图标
    const playIcon = button.querySelector('.play-icon');
    const loadingIcon = button.querySelector('.loading-icon');
    
    if (playIcon && loadingIcon) {
      playIcon.classList.add('hidden');
      loadingIcon.classList.remove('hidden');
    }
    
    // 获取按钮对应的曲目信息
    const trackName = button.getAttribute('data-track-name');
    const trackArtistsRaw = button.getAttribute('data-track-artists');

    // 获取originalContent数据
    const trackOriginalContentStr = button.getAttribute('data-track-original-content');
    let parsedOriginalContent = {};
    originalContentObj = {};

    try {
      if (trackOriginalContentStr) {
        // 先尝试直接解析
        try {
          parsedOriginalContent = JSON.parse(trackOriginalContentStr);
        } catch (parseError) {
          console.error('First-level parsing error:', parseError);
          
          // 如果直接解析失败，可能是因为字符串已经被转义了
          // 尝试对内容进行二次解析
          try {
            // 将转义字符替换回来，修复可能的双重转义问题
            const fixedStr = trackOriginalContentStr
              .replace(/\\\\(["'])/g, '\\$1') // 修复双重转义
              .replace(/\\([^"'\\])/g, '$1'); // 移除不必要的转义
            
            originalContentObj = JSON.parse(fixedStr);
            
            // 如果内容是字符串，可能需要再次解析
            if (typeof originalContentObj === 'string') {
              originalContentObj = JSON.parse(originalContentObj);
            }
          } catch (secondError) {
            console.error('Second-level parsing error:', secondError);
          }
        }
      }
    } catch (e) {
      console.error('Error handling originalContent:', e);
    }

    // 获取音频URL
    // 尝试从多个可能的来源获取URL
    let trackAudioUrl = parsedOriginalContent.url || 
                      (originalContentObj && originalContentObj.url) || 
                      button.getAttribute('data-track-audio');
    
    // 如果还是没有找到URL，尝试使用originalUrl
    if (!trackAudioUrl) {
      // 直接使用originalUrl作为备选音频源
      trackAudioUrl = button.getAttribute('data-track-original-url');
      console.log('Using originalUrl as fallback:', trackAudioUrl);
    }
    
    // 输出所有可能的音频源信息供调试
    console.log('Track audio URL:', trackAudioUrl);
    console.log('data-track-audio:', button.getAttribute('data-track-audio'));
    console.log('data-track-original-url:', button.getAttribute('data-track-original-url'));
    console.log('Original content string:', trackOriginalContentStr);
    console.log('Original content (parsed):', parsedOriginalContent);
    console.log('Original content (fixed):', originalContentObj);

    if (trackAudioUrl) {
      // 获取专辑名称
      const albumTitle = document.querySelector('h1')?.textContent || '';

      // 获取曲目索引
      const trackIndex = parseInt(button.getAttribute('data-track-index') || '0');

      // 获取 originalContent 中的艺术家信息
      const artistInfo = parsedOriginalContent.artists || trackArtistsRaw || '';

      // 创建曲目对象，确保包含必要字段
      const track = {
        name: trackName,
        title: trackName,
        artists: trackArtistsRaw ? trackArtistsRaw.split(', ') : [],
        url: trackAudioUrl, // 直接使用originalContent.url
        audioUrl: trackAudioUrl, // 保持url和audioUrl一致
        albumCover: document.querySelector('#album-cover')?.getAttribute('src') || '',
        album: albumTitle,
        artist: artistInfo,
        id: `track-${trackName}-${Date.now()}`
      };

      // 直接调用全局的 playTrack 函数，确保艺术家信息能够正确保存到播放历史记录中
      if (window.playTrack && typeof window.playTrack === 'function') {
        console.log('Using window.playTrack function with artist info:', artistInfo);
        // 使用window.tracksData而不是eraData.tracks
        window.playTrack(track, window.tracksData || [], trackIndex);
      } else {
        // 如果全局函数不可用，使用自定义事件
        const playEvent = new CustomEvent('play-track', {
          detail: {
            track,
            playlist: window.tracksData || [],
            index: trackIndex
          }
        });

        document.dispatchEvent(playEvent);
      }

      // 监听音频加载完成事件，恢复按钮状态
      document.addEventListener('audio-loaded', function resetButtonState() {
        // 只处理当前播放按钮的状态
        if (currentPlayingButton === button) {
          button.classList.remove('loading');
          button.classList.add('playing');
          
          if (playIcon && loadingIcon) {
            loadingIcon.classList.add('hidden');
            playIcon.classList.remove('hidden');
          }
        }
        // 移除事件监听器，避免重复执行
        document.removeEventListener('audio-loaded', resetButtonState);
      });

      // 监听音频加载失败事件，恢复按钮状态
      document.addEventListener('audio-error', function resetButtonState() {
        // 只处理当前播放按钮的状态
        if (currentPlayingButton === button) {
          button.classList.remove('loading');
          
          if (playIcon && loadingIcon) {
            loadingIcon.classList.add('hidden');
            playIcon.classList.remove('hidden');
          }
        }
        // 移除事件监听器，避免重复执行
        document.removeEventListener('audio-error', resetButtonState);
      });
    }
  }
  
  // 重置所有按钮状态
  function resetAllButtonsState() {
    const allButtons = document.querySelectorAll('.play-button');
    allButtons.forEach(btn => {
      btn.classList.remove('loading', 'playing');
      
      const playIcon = btn.querySelector('.play-icon');
      const loadingIcon = btn.querySelector('.loading-icon');
      
      if (playIcon && loadingIcon) {
        loadingIcon.classList.add('hidden');
        playIcon.classList.remove('hidden');
      }
    });
  }
  
  // 设置曲目的notes信息显示 - 用于初始加载的曲目
  function setupNotesInfo() {
    // 为所有曲目设置notes信息
    // 为每一行设置悬浮提示
    const rows = document.querySelectorAll('tbody tr');
    setupNotesInfoForRows(rows);
  }
  
  // 仅为新添加的曲目设置notes信息
  function setupNotesInfoForNewRows(newRows) {
    setupNotesInfoForRows(newRows);
  }
  
  // 为指定的行设置notes信息
  function setupNotesInfoForRows(rows) {
    if (!rows || rows.length === 0) return;
    
    // 遍历每一行
    rows.forEach(row => {
      // 检查这一行是否有track-content元素
      const trackContent = row.querySelector('.track-content');
      if (!trackContent) return;
      
      // 检查这一行是否有notes数据且未初始化
      // 注意：notes-info-button是隐藏的，所以我们不使用:not([data-initialized])选择器
      const notesButton = row.querySelector('.notes-info-button');
      if (!notesButton) return;
      
      // 如果按钮已经初始化过，则跳过
      if (notesButton.hasAttribute('data-initialized')) return;
      
      // 标记按钮已初始化
      notesButton.setAttribute('data-initialized', 'true');
      
      // 获取notes内容
      const notesContentElement = notesButton.nextElementSibling;
      if (!notesContentElement) return;
      
      const notesContent = notesContentElement.textContent.trim();
      if (!notesContent) return;
      
      // 创建悬浮提示
      const tooltip = document.createElement('div');
      tooltip.className = 'desktop-tooltip fixed bg-black/90 p-3 rounded-md shadow-lg text-sm text-white z-20 max-w-md hidden';
      tooltip.textContent = notesContent;
      document.body.appendChild(tooltip);
      
      // 鼠标悬停显示
      trackContent.addEventListener('mouseenter', (e) => {
        if (window.innerWidth >= 640) { // 只在桌面端显示
          tooltip.classList.remove('hidden');
          window.positionTooltip(e, tooltip);
        }
      });
      
      // 鼠标移动更新位置
      trackContent.addEventListener('mousemove', (e) => {
        if (window.innerWidth >= 640) {
          window.positionTooltip(e, tooltip);
        }
      });
      
      // 鼠标离开隐藏
      trackContent.addEventListener('mouseleave', () => {
        tooltip.classList.add('hidden');
      });
    });
  }

  // 处理曲目时长，将数字转换为分:秒格式
  function formatTrackLengths() {
    // 处理时长格式
    const lengthElements = document.querySelectorAll('.track-length');
    lengthElements.forEach(element => {
      const lengthValue = element.getAttribute('data-length');
      if (lengthValue) {
        // 如果已经是分:秒格式，保持不变
        if (/^\d+:\d{2}$/.test(lengthValue)) {
          element.textContent = lengthValue;
          return;
        }

        // 尝试将字符串转换为数字
        const numValue = parseFloat(lengthValue);
        if (!isNaN(numValue)) {
          // 转换为分:秒格式
          const minutes = Math.floor(numValue / 60);
          const seconds = Math.floor(numValue % 60);
          element.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        } else {
          element.textContent = '';
        }
      } else {
        element.textContent = '';
      }
    });
  }
</script>
