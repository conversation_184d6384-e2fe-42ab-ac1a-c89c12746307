---
import MainLayout from '../layouts/MainLayout.astro';
import { t } from '../i18n/index.js';

// Get language from URL path segments
const getLanguageFromPath = (url) => {
  const segments = url.pathname.split('/').filter(Boolean);
  if (segments.length > 0 && ['en', 'ar', 'pt'].includes(segments[0])) {
    return segments[0];
  }
  return 'en'; // Default to English
};

const lang = getLanguageFromPath(Astro.url);
---

<MainLayout title={`404 - ${t('common.pageNotFound', lang)} | TrackerHub`} description={t('common.pageNotFoundDesc', lang)}>
  <div class="flex flex-col items-center justify-center min-h-[70vh] text-center px-4">
    <h1 class="text-5xl font-bold mb-4">404</h1>
    <h2 class="text-2xl font-semibold mb-6">{t('common.pageNotFound', lang)}</h2>
    <p class="text-lg mb-8">{t('common.pageNotFoundDesc', lang)}</p>
    <a href="/" class="px-6 py-3 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors">
      {t('common.backToHome', lang)}
    </a>
  </div>
</MainLayout>
