---
import MainLayout from '../../layouts/MainLayout.astro';
import { promises as fs } from 'fs';
import path from 'path';

// 启用预渲染
export const prerender = true;

// 获取所有分类用于静态生成
export async function getStaticPaths() {
  const yeTrackerDir = path.join(process.cwd(), 'data', 'YeTracker');
  const paths = [];

  try {
    const files = await fs.readdir(yeTrackerDir);
    const categories = files
      .filter(file => file.endsWith('.html') && !file.includes('Template') && !file.includes('Key'))
      .map(file => ({
        id: file.replace('.html', '').toLowerCase().replace(/\s+/g, '-'),
        name: file.replace('.html', '')
      }));

    // 只为有实际数据的分类生成页面
    const validCategories = ['unreleased', 'best-of', 'recent', 'art'];

    for (const category of categories) {
      // 只包含有数据的分类
      if (validCategories.includes(category.id)) {
        paths.push({
          params: { categoryId: category.id },
          props: { categoryName: category.name }
        });
      }
    }
  } catch (error) {
    console.error('Error loading YeTracker categories:', error);
  }

  return paths;
}

// 获取特定分类数据
const { categoryId } = Astro.params;
const { categoryName } = Astro.props;

// 面包屑导航数据
const breadcrumbs = [
  { name: 'Home', url: '/' },
  { name: 'Artists', url: '/artists' },
  { name: 'Ye', url: '/artists/ye' },
  { name: categoryName, url: `/categories/${categoryId}`, current: true }
];

// 从JSON文件加载分类下的专辑数据
const artistId = 'ye';
let categoryInfo = null;
let albums = [];

try {
  // 尝试加载分类信息
  const categoryInfoPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'info.json');
  const categoryErasPath = path.join(process.cwd(), 'public', 'data', 'artists', artistId, 'categories', categoryId, 'eras.json');
  
  try {
    const categoryInfoData = await fs.readFile(categoryInfoPath, 'utf-8');
    categoryInfo = JSON.parse(categoryInfoData);
  } catch (infoError) {
    console.error(`Category info not found for ${categoryId}:`, infoError);
  }
  
  // 只有 unreleased 分类才有 eras.json 文件
  if (categoryId === 'unreleased') {
    try {
      const erasData = await fs.readFile(categoryErasPath, 'utf-8');
      albums = JSON.parse(erasData);

      // 按照专辑发行顺序排序
      const eraOrder = [
        'before-the-college-dropout',
        'the-college-dropout',
        'late-registration',
        'graduation',
        '808s-heartbreak',
        'my-beautiful-dark-twisted-fantasy',
        'watch-the-throne',
        'cruel-summer',
        'yeezus',
        'the-life-of-pablo',
        'ye',
        'kids-see-ghosts',
        'jesus-is-king',
        'donda',
        'donda-2',
        'vultures'
      ];

      albums.sort((a, b) => {
        const indexA = eraOrder.indexOf(a.id);
        const indexB = eraOrder.indexOf(b.id);
        return indexA - indexB;
      });
    } catch (erasError) {
      console.error(`Eras data not found for category ${categoryId}:`, erasError);
    }
  }
} catch (error) {
  console.error(`Error loading category data for ${categoryId}:`, error);
}

// 如果没有找到数据，使用默认模拟数据
if (albums.length === 0) {
  albums = [
    {
      id: 'the-college-dropout',
      name: 'The College Dropout',
      description: 'Debut studio album',
      trackCount: 21,
      backgroundColor: '#8B4513',
      coverImage: '/images/eras/default.jpg',
      artist: 'Ye',
      artistId: 'ye'
    }
  ];
}
---

<MainLayout title={`${categoryName} - Ye - TrackerHive`}>
  <main class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 面包屑导航 -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbs.map((breadcrumb, index) => (
          <li class="inline-flex items-center">
            {index > 0 && (
              <svg class="w-3 h-3 mx-1 text-text-secondary" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
              </svg>
            )}
            <a 
              href={breadcrumb.url} 
              class={`inline-flex items-center text-sm font-medium ${
                breadcrumb.current 
                  ? 'text-primary cursor-default' 
                  : 'text-text-secondary hover:text-primary'
              }`}
              aria-current={breadcrumb.current ? 'page' : undefined}
            >
              {breadcrumb.name}
            </a>
          </li>
        ))}
      </ol>
    </nav>
    
    <!-- 分类头部 -->
    <div class="mb-10 rounded-xl overflow-hidden shadow-lg bg-dark-elevated">
      <div class="px-8 py-12 md:py-16">
        <div class="md:max-w-3xl">
          <h1 class="text-3xl md:text-4xl font-bold mb-4 text-white">{categoryName}</h1>
          <p class="text-base md:text-lg text-text-secondary">
            {categoryName === 'Released' && 'Browse official albums and singles released by Ye throughout his career.'}
            {categoryName === 'Unreleased' && 'Explore leaked and unreleased tracks from various eras of Ye\'s career.'}
            {categoryName === 'Stems' && 'Access isolated vocal, instrumental, and other track components from Ye\'s discography.'}
            {categoryName === 'Remixes' && 'Discover official and unofficial remixes of Ye\'s tracks.'}
            {categoryName === 'Tracklists' && 'View compilations of album and project tracklists from Ye\'s career.'}
            {categoryName === 'Recent' && 'Check out recently added or updated tracks in the Ye catalog.'}
            {!['Released', 'Unreleased', 'Stems', 'Remixes', 'Tracklists', 'Recent'].includes(categoryName) && `Browse ${categoryName.toLowerCase()} content from Ye's discography.`}
          </p>
        </div>
      </div>
    </div>
    
    <!-- 专辑时期列表 -->
    <section>
      <h2 class="text-2xl font-bold text-white mb-6">Album Eras ({albums.length})</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {albums.map((era) => (
          <a href={`/eras/${era.id}`} class="group">
            <div 
              class="relative overflow-hidden rounded-xl shadow-md hover:shadow-lg transition-all border border-dark-hover hover:border-primary/50"
              style={`background-color: ${era.backgroundColor || '#333'};`}
            >
              <div class="aspect-square overflow-hidden relative">
                <img 
                  src={era.coverImage} 
                  alt={`${era.name} cover`} 
                  class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end p-4">
                  <div>
                    <h3 class="text-lg font-bold text-white group-hover:text-primary transition-colors">{era.name}</h3>
                    <p class="text-sm text-gray-300">{era.trackCount} tracks</p>
                  </div>
                </div>
              </div>
            </div>
          </a>
        ))}
      </div>
    </section>
  </main>
</MainLayout>
