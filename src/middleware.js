// 多语言中间件
import { DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES } from './i18n';

export function onRequest({ locals, request }, next) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // 从路径中提取语言代码
  const segments = pathname.split('/').filter(Boolean);
  const maybeLocale = segments[0];
  
  // 检查是否是支持的语言代码
  if (maybeLocale && Object.keys(SUPPORTED_LANGUAGES).includes(maybeLocale)) {
    // 设置当前语言
    locals.lang = maybeLocale;
    
    // 如果是默认语言，重定向到无前缀版本（可选）
    if (maybeLocale === DEFAULT_LANGUAGE && segments.length > 1) {
      return new Response(null, {
        status: 301,
        headers: {
          'Location': `/${segments.slice(1).join('/')}`
        }
      });
    }
  } else {
    // 如果没有语言前缀，使用默认语言
    locals.lang = DEFAULT_LANGUAGE;
  }
  
  // 设置文档方向
  locals.dir = SUPPORTED_LANGUAGES[locals.lang]?.dir || 'ltr';
  
  return next();
}
