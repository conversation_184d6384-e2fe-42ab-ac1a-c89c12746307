{"artists": [{"id": "ye", "name": "Ye", "aliases": ["Kanye West", "Yeezy"], "image": "/images/artists/ye.jpg", "description": "<PERSON> (formerly known as <PERSON><PERSON><PERSON>) is an American rapper, record producer, and fashion designer. He is one of the most influential hip hop artists of all time.", "detailedDescription": "<PERSON><PERSON><PERSON>, known professionally as <PERSON> (formerly <PERSON><PERSON><PERSON>), is an American rapper, record producer, and fashion designer. With a career spanning over two decades, he has become one of the most influential and controversial artists of his generation, known for albums like \"The College Dropout,\" \"My Beautiful Dark Twisted Fantasy,\" and \"Donda.\"", "featured": true, "albumCount": 10, "trackCount": 137, "categories": [{"id": "released", "name": "Released", "description": "Browse official albums and singles released by <PERSON> throughout his career.", "hasData": true}, {"id": "unreleased", "name": "Unreleased", "description": "Explore leaked and unreleased tracks from various eras of <PERSON>'s career.", "hasData": true}, {"id": "stems", "name": "Stems", "description": "Access isolated vocal, instrumental, and other track components from <PERSON>'s discography.", "hasData": false}, {"id": "tracklists", "name": "Tracklists", "description": "View compilations of album and project tracklists from <PERSON>'s career.", "hasData": false}, {"id": "recent", "name": "Recent", "description": "Check out recently added or updated tracks in the Ye catalog.", "hasData": true}, {"id": "art", "name": "Art", "description": "View artwork and visual media created by or associated with <PERSON>.", "hasData": true}, {"id": "best-of", "name": "Best Of", "description": "Explore curated collections of <PERSON>'s best work across different eras and styles.", "hasData": true}, {"id": "album-copies", "name": "Album Copies", "description": "Access alternative versions and copies of <PERSON>'s official album releases.", "hasData": false}, {"id": "fakes", "name": "Fakes", "description": "Learn about and identify fake or misattributed Ye tracks circulating online.", "hasData": false}, {"id": "grails", "name": "Grails", "description": "Discover the most sought-after unreleased Ye tracks and snippets.", "hasData": false}, {"id": "groupbuys", "name": "Groupbuys", "description": "Information about community efforts to purchase unreleased Ye music.", "hasData": false}, {"id": "misc", "name": "Misc", "description": "Miscellaneous Ye content that doesn't fit into other categories.", "hasData": false}, {"id": "special", "name": "Special", "description": "Special collections and unique content related to <PERSON>'s career.", "hasData": false}, {"id": "worst-of", "name": "Worst Of", "description": "A collection of <PERSON>'s least popular or most controversial works.", "hasData": false}]}, {"id": "playboi-carti", "name": "<PERSON><PERSON><PERSON>", "aliases": ["<PERSON>", "King <PERSON>amp", "<PERSON>"], "image": "/images/artists/playboi-carti.jpg", "description": "<PERSON>, known professionally as <PERSON><PERSON><PERSON>, is an American rapper and singer. He is recognized for his experimental musical style, gothic fashion, and mysterious public persona.", "featured": true, "albumCount": 3, "trackCount": 58, "categories": [{"id": "art", "name": "Art", "description": "Explore <PERSON><PERSON><PERSON>'s artistic works and visual content."}, {"id": "best-of", "name": "Best Of", "description": "Curated collection of <PERSON><PERSON><PERSON>'s best tracks."}, {"id": "recent", "name": "Recent", "description": "Check out recently added or updated tracks in the Playboi Carti catalog."}, {"id": "unreleased", "name": "Unreleased", "description": "Explore leaked and unreleased tracks from various eras of <PERSON><PERSON><PERSON>'s career."}, {"id": "released", "name": "Released", "description": "Browse official albums and singles released by <PERSON><PERSON><PERSON> throughout his career.", "hasData": false}, {"id": "stems", "name": "Stems", "description": "Access isolated vocal, instrumental, and other track components from <PERSON><PERSON><PERSON>'s discography.", "hasData": false}, {"id": "remixes", "name": "Remixes", "description": "Discover official and unofficial remixes of <PERSON><PERSON><PERSON>'s tracks.", "hasData": false}, {"id": "tracklists", "name": "Tracklists", "description": "View compilations of album and project tracklists from <PERSON><PERSON><PERSON>'s career.", "hasData": false}]}]}