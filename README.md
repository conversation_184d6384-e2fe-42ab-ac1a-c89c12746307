# TrackerHub - Music Artist Tracking Platform

TrackerHub是一个专注于提供音乐艺术家更新的网站平台，特别优化了对Ye（坎耶·韦斯特）的追踪功能。该网站旨在为音乐爱好者提供实时、准确的艺术家动态，包括新专辑发布、单曲发行、合作项目等信息。

## 项目结构



```
TrackerHub/
├── index.html             # 网站首页
├── styles.css             # 全局样式表
├── script.js              # 全局JavaScript功能
├── artists/               # 艺术家页面目录
│   ├── index.html         # 所有艺术家列表页
│   ├── ye.html            # Ye专属追踪页面（重点优化）
│   └── [其他艺术家].html   # 其他艺术家页面
├── images/                # 网站所需图片资源
│   ├── ye.jpg             # Ye主图
│   ├── carti.jpg          # Carti主图
│   └── ...                # 其他图片资源
└── README.md              # 项目说明文档
```

## SEO优化策略

本网站针对以下关键词进行了优化：

1. **ye tracker** - 主要目标关键词（13K流量，37K搜索量）
2. yetracker
3. kanye tracker
4. carti tracker
5. trackerhub

在优化过程中，我们特别注意：

- 关键词密度控制在3%以内
- 标题、元描述、URL结构的合理设置
- 内容与用户意图的匹配
- 页面加载速度优化
- 移动端友好设计

## 关键页面说明

### 首页 (index.html)
首页展示网站的主要功能和特点，突出展示了几个热门艺术家的追踪卡片，其中Ye的追踪卡片被放在最显眼的位置。

### Ye专属页面 (artists/ye.html)
这是网站的核心页面，专门用于追踪Ye的最新音乐动态。页面包含：
- 完整的唱片目录
- 最新更新信息
- 即将发布的项目预告
- 用户订阅模块

## 设计理念

网站采用现代化UI设计，主色调为紫色渐变，搭配黄色作为强调色，给用户带来专业、时尚的视觉体验。布局上采用卡片式设计，便于用户快速获取信息。

## 技术栈

- HTML5
- CSS3（含响应式设计）
- JavaScript（纯原生，无框架）

## 未来开发计划

1. 添加用户账户系统，允许用户自定义追踪的艺术家
2. 实现推送通知功能
3. 增加社区讨论板块
4. 实现更多艺术家页面
5. 添加音乐播放器功能

```sh
npm create astro@latest -- --template basics
```

[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/withastro/astro/tree/latest/examples/basics)
[![Open with CodeSandbox](https://assets.codesandbox.io/github/button-edit-lime.svg)](https://codesandbox.io/p/sandbox/github/withastro/astro/tree/latest/examples/basics)
[![Open in GitHub Codespaces](https://github.com/codespaces/badge.svg)](https://codespaces.new/withastro/astro?devcontainer_path=.devcontainer/basics/devcontainer.json)

> 🧑‍🚀 **Seasoned astronaut?** Delete this file. Have fun!

![just-the-basics](https://github.com/withastro/astro/assets/2244813/a0a5533c-a856-4198-8470-2d67b1d7c554)

## 🚀 Project Structure

Inside of your Astro project, you'll see the following folders and files:

```text
/
├── public/
│   └── favicon.svg
├── src/
│   ├── layouts/
│   │   └── Layout.astro
│   └── pages/
│       └── index.astro
└── package.json
```

To learn more about the folder structure of an Astro project, refer to [our guide on project structure](https://docs.astro.build/en/basics/project-structure/).

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`      |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check` |
| `npm run astro -- --help` | Get help using the Astro CLI                     |

## 👀 Want to learn more?

Feel free to check [our documentation](https://docs.astro.build) or jump into our [Discord server](https://astro.build/chat).
