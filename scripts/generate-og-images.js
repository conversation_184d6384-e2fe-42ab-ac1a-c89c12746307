import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';
import { createCanvas, registerFont, loadImage } from 'canvas';

const ARTISTS = ['ye', 'playboi-carti'];
const LANGUAGES = ['en', 'ar', 'pt'];

async function generateOGImage(artist, category, lang) {
  const width = 1200;
  const height = 630;
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // 设置背景
  try {
    // 尝试加载艺术家背景图片
    const bgPath = path.join(process.cwd(), 'public', 'images', 'artists', artist, 'background.jpg');
    const bgExists = await fs.access(bgPath).then(() => true).catch(() => false);
    
    if (bgExists) {
      const bgImage = await loadImage(bgPath);
      ctx.drawImage(bgImage, 0, 0, width, height);
    } else {
      // 使用渐变背景
      const gradient = ctx.createLinearGradient(0, 0, width, height);
      gradient.addColorStop(0, '#1a1a1a');
      gradient.addColorStop(1, '#2a2a2a');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, width, height);
    }

    // 添加半透明黑色遮罩
    ctx.fillStyle = 'rgba(0, 0, 0, 0.6)';
    ctx.fillRect(0, 0, width, height);

    // 设置文字样式
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // 添加艺术家名称
    ctx.font = 'bold 72px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    ctx.fillStyle = '#ffffff';
    ctx.fillText(artist.toUpperCase(), width / 2, height / 2 - 50);

    // 添加分类名称
    ctx.font = '48px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    ctx.fillStyle = '#f0f0f0';
    ctx.fillText(category.toUpperCase(), width / 2, height / 2 + 50);

    // 添加网站水印
    ctx.font = '24px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
    ctx.fillStyle = '#808080';
    ctx.fillText('TRACKERHIVE.COM', width / 2, height - 40);

    // 将Canvas转换为图片并优化
    const buffer = canvas.toBuffer('image/jpeg');
    await sharp(buffer)
      .jpeg({ quality: 90, progressive: true })
      .toFile(path.join(process.cwd(), 'public', 'images', 'artists', artist, `${category}-og.jpg`));

    console.log(`✅ Generated OG image for ${artist}/${category}`);
  } catch (error) {
    console.error(`❌ Error generating OG image for ${artist}/${category}:`, error);
    throw error;
  }
}

async function main() {
  // 确保目录存在
  for (const artist of ARTISTS) {
    const artistDir = path.join(process.cwd(), 'public', 'images', 'artists', artist);
    await fs.mkdir(artistDir, { recursive: true });
  }

  // 只为有数据的分类生成图片
  const categories = [
    'art', 'best-of', 'recent', 'unreleased'
  ];

  // 为每个艺术家的每个分类生成图片
  for (const artist of ARTISTS) {
    for (const category of categories) {
      console.log(`Generating OG image for ${artist}/${category}...`);
      await generateOGImage(artist, category);
    }
  }
}

main().catch(console.error);
