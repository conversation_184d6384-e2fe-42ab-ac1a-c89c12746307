import fs from 'fs/promises';
import path from 'path';
import { SitemapStream, streamToPromise } from 'sitemap';
import { Readable } from 'stream';
import xmlFormatter from 'xml-formatter';

const SITE_URL = 'https://trackerhub.co';
const DEFAULT_LANG = 'en';
const LANGUAGES = ['en', 'ar', 'pt'];
const ARTISTS = ['ye', 'playboi-carti'];

// 只包含有数据的分类
const YE_CATEGORIES = [
  { id: 'art', priority: 0.7 },
  { id: 'recent', priority: 0.7 },
  { id: 'best-of', priority: 0.7 },
  { id: 'unreleased', priority: 0.8 }
];

// Playboi <PERSON>ti的分类
const CARTI_CATEGORIES = [
  { id: 'art', priority: 0.6 },
  { id: 'best-of', priority: 0.7 },
  { id: 'recent', priority: 0.7 },
  { id: 'unreleased', priority: 0.7 }
];

// 添加 Ye 的完整 eras 列表
const YE_ERAS = [
  '808s-heartbreak', 'bad-bitch-playbook', 'before-the-college-dropout',
  'bully-v1', 'bully-v2', 'cruel-summer', 'cruel-winter-v1', 'cruel-winter-v2',
  'donda-2', 'donda-v1', 'donda-v2', 'donda-v3', 'good-ass-job', 'graduation',
  'hitler', 'jesus-is-king', 'jesus-is-lord', 'kids-see-ghosts',
  'late-registration', 'my-beautiful-dark-twisted-fantasy', 'so-help-me-god',
  'swish', 'thank-god-for-drugs', 'the-college-dropout', 'the-life-of-pablo',
  'turbografx16', 'vultures-1', 'vultures-2', 'vultures-3', 'war',
  'watch-the-throne', 'yandhi-v1', 'yandhi-v2', 'ye', 'yebu',
  'yeezus-2', 'yeezus'
];

// 添加 Playboi Carti 的完整 eras 列表
const CARTI_ERAS = [
  '004kt', '16-29-v1', '808s-heartbreak', 'aviation-class', 'awful-records',
  'bad-bitch-playbook', 'before-the-college-dropout', 'bully-v1', 'bully-v2',
  'ca-h-carti-season', 'cash-carti-the-mixtape', 'collaboration-with-digital-nas',
  'cruel-summer', 'cruel-winter-v1', 'cruel-winter-v2', 'die-lit', 'die-lit-2',
  'donda-2', 'donda-v1', 'donda-v2', 'donda-v3', 'good-ass-job', 'graduation',
  'guapo-era', 'hitler', 'jesus-is-king', 'jesus-is-lord', 'kids-see-ghosts',
  'kream', 'late-registration', 'music-cave-sessions', 'music-v2-i-am-music-sessions',
  'music-v3-final-sessions', 'my-beautiful-dark-twisted-fantasy', 'narcissist',
  'no-pressure', 'playboi-carti', 'sen-ation', 'so-help-me-god', 'swish',
  'thank-god-for-drugs', 'the-college-dropout', 'the-high-chronical',
  'the-life-of-pablo', 'tmb-collab', 'trippie-redd-collab-ep', 'turbografx16',
  'vultures', 'vultures-1', 'vultures-2', 'vultures-3', 'war', 'watch-the-throne',
  'whole-lotta-red', 'whole-lotta-red-v1', 'whole-lotta-red-v2', 'whole-lotta-red-v3',
  'whole-lotta-red-v4', 'yandhi-v1', 'yandhi-v2', 'ye', 'ye-donda', 'yebu',
  'yeezus', 'yeezus-2', 'young-mi-fit'
];

// 格式化并保存XML
async function saveFormattedXml(data, filename) {
  const formattedXml = xmlFormatter(data.toString(), {
    indentation: '  ',
    collapseContent: true,
    lineSeparator: '\n'
  });
  await fs.writeFile(filename, formattedXml);
}

// 处理URL前缀，英文版不带语言前缀
function getLocalizedUrl(url, lang) {
  return lang === DEFAULT_LANG ? url : `/${lang}${url}`;
}

async function generateMainSitemap() {
  const links = [
    { url: '/', changefreq: 'daily', priority: 1.0 },
    ...LANGUAGES.filter(lang => lang !== DEFAULT_LANG).map(lang => ({
      url: `/${lang}`,
      changefreq: 'daily',
      priority: 0.9
    })),
    { url: '/artists', changefreq: 'daily', priority: 0.8 },
    ...LANGUAGES.filter(lang => lang !== DEFAULT_LANG).map(lang => ({
      url: `/${lang}/artists`,
      changefreq: 'daily',
      priority: 0.8
    }))
  ];

  const stream = new SitemapStream({ hostname: SITE_URL });
  const data = await streamToPromise(Readable.from(links).pipe(stream));
  await saveFormattedXml(data, path.join(process.cwd(), 'public', 'sitemap.xml'));
}

async function generateArtistsSitemap() {
  const links = LANGUAGES.flatMap(lang =>
    ARTISTS.map(artist => ({
      url: getLocalizedUrl(`/artists/${artist}`, lang),
      changefreq: 'daily',
      priority: 0.8
    }))
  );

  const stream = new SitemapStream({ hostname: SITE_URL });
  const data = await streamToPromise(Readable.from(links).pipe(stream));
  await saveFormattedXml(data, path.join(process.cwd(), 'public', 'sitemap-artists.xml'));
}

async function generateCategoriesSitemap() {
  const links = LANGUAGES.flatMap(lang => {
    const yeCategories = YE_CATEGORIES.map(category => ({
      url: `/artists/ye/${category.id}`,
      changefreq: 'daily',
      priority: category.priority
    }));

    const cartiCategories = CARTI_CATEGORIES.map(category => ({
      url: `/artists/playboi-carti/${category.id}`,
      changefreq: 'daily',
      priority: category.priority
    }));

    return [...yeCategories, ...cartiCategories].map(item => ({
      ...item,
      url: getLocalizedUrl(item.url, lang)
    }));
  });

  const stream = new SitemapStream({ hostname: SITE_URL });
  const data = await streamToPromise(Readable.from(links).pipe(stream));
  await saveFormattedXml(data, path.join(process.cwd(), 'public', 'sitemap-categories.xml'));
}

async function generateErasSitemap() {
  const links = LANGUAGES.flatMap(lang => [
    // Ye eras
    ...YE_ERAS.map(era => ({
      url: getLocalizedUrl(`/artists/ye/unreleased/eras/${era}`, lang),
      changefreq: 'weekly',
      priority: 0.6
    })),
    // Playboi Carti eras
    ...CARTI_ERAS.map(era => ({
      url: getLocalizedUrl(`/artists/playboi-carti/unreleased/eras/${era}`, lang),
      changefreq: 'weekly',
      priority: 0.6
    }))
  ]);

  const stream = new SitemapStream({ hostname: SITE_URL });
  const data = await streamToPromise(Readable.from(links).pipe(stream));
  await saveFormattedXml(data, path.join(process.cwd(), 'public', 'sitemap-eras.xml'));
}

// 专门生成 Playboi Carti 的 eras 站点地图，匹配 sitemap-index.xml 中的条目
async function generateCartiErasSitemap() {
  const links = LANGUAGES.flatMap(lang =>
    CARTI_ERAS.map(era => ({
      url: getLocalizedUrl(`/artists/playboi-carti/unreleased/eras/${era}`, lang),
      changefreq: 'weekly',
      priority: 0.6
    }))
  );

  const stream = new SitemapStream({ hostname: SITE_URL });
  const data = await streamToPromise(Readable.from(links).pipe(stream));
  await saveFormattedXml(data, path.join(process.cwd(), 'public', 'sitemap-carti-eras.xml'));
}


async function main() {
  try {
    await Promise.all([
      generateMainSitemap(),
      generateArtistsSitemap(),
      generateCategoriesSitemap(),
      generateErasSitemap(),
      generateCartiErasSitemap()
    ]);
    console.log('所有 sitemap 文件已成功生成');
  } catch (error) {
    console.error('生成 sitemap 时发生错误:', error);
    process.exit(1);
  }
}

main();
