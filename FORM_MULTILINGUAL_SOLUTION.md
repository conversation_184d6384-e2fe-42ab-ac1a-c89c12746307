# TrackerHub 表单多语言与专辑选择解决方案

## 🎯 解决方案概述

本解决方案实现了一个具有高扩展性的多语言表单系统，支持动态专辑加载，完美解决了您提到的问题：

1. ✅ **多语言支持** - 表单提示信息根据当前语言自动切换
2. ✅ **专辑选择功能** - 根据艺术家和分类动态加载专辑列表
3. ✅ **自动化数据提取** - 脚本自动从tracks.json提取专辑信息
4. ✅ **高扩展性** - 易于添加新语言和新艺术家

## 📁 文件结构

```
public/data/
├── i18n/
│   └── form-translations.json          # 表单专用翻译文件
└── artists/
    └── ye/
        └── categories/
            ├── unreleased/
            │   ├── albums.json         # 自动生成的专辑列表
            │   └── eras.json          # 原始数据源
            ├── art/
            │   ├── albums.json         # 自动生成的专辑列表
            │   └── tracks.json        # 原始数据源
            └── ...

scripts/
└── extract-albums.js                  # 专辑提取脚本

src/components/content-submission/
└── SimpleSubmissionForm.astro         # 更新的表单组件
```

## 🚀 核心功能

### 1. 多语言表单翻译

**支持的语言**: 英语(en)、阿拉伯语(ar)、葡萄牙语(pt)

**翻译内容包括**:
- 表单标题和描述
- 字段标签和占位符
- 验证错误消息
- 按钮文本
- 成功提示

**示例**:
```json
{
  "en": {
    "form": {
      "title": "Add Track",
      "fields": {
        "artist": "Artist",
        "category": "Category",
        "album": "Album"
      },
      "validation": {
        "fillAllFields": "Please fill in all fields"
      }
    }
  }
}
```

### 2. 动态专辑加载

**工作流程**:
1. 用户选择艺术家 → 触发专辑加载
2. 用户选择分类 → 根据艺术家+分类加载对应专辑
3. 专辑列表动态更新

**API端点**: `/data/artists/{artistId}/categories/{categoryId}/albums.json`

### 3. 自动化专辑提取

**脚本功能**:
- 从 `tracks.json` 提取专辑名称
- 从 `eras.json` 提取时期信息
- 自动去重和排序
- 生成标准化的 `albums.json` 文件

**运行方式**:
```bash
# 手动运行
npm run extract-albums

# 构建时自动运行
npm run build
```

## 🛠️ 技术实现

### 1. 服务端渲染 (SSR)

```astro
---
// 在Astro组件中加载翻译
const currentLang = getLanguageFromURL(Astro.url.href);
const translationsResponse = await fetch('/data/i18n/form-translations.json');
const formTranslations = allTranslations[currentLang] || allTranslations['en'];
---

<h3>{formTranslations.form.title}</h3>
```

### 2. 客户端动态加载

```javascript
// 动态加载专辑
async function loadAlbums(artistId, categoryId) {
  const response = await fetch(`/data/artists/${artistId}/categories/${categoryId}/albums.json`);
  const albums = await response.json();
  // 更新选择器选项
}
```

### 3. 数据提取脚本

```javascript
// 从不同数据源提取专辑
async function extractAlbumsFromTracks(tracksPath) {
  const tracksData = JSON.parse(await fs.readFile(tracksPath, 'utf-8'));
  // 处理不同的数据结构
  // 返回标准化的专辑列表
}
```

## 📊 扩展性设计

### 添加新语言

1. 在 `form-translations.json` 中添加新语言:
```json
{
  "es": {
    "form": {
      "title": "Agregar Pista",
      // ... 其他翻译
    }
  }
}
```

2. 在 `i18n/index.js` 中注册新语言

### 添加新艺术家

1. 创建艺术家目录结构:
```
public/data/artists/new-artist/
├── categories/
│   ├── released/tracks.json
│   └── unreleased/eras.json
```

2. 运行提取脚本:
```bash
npm run extract-albums
```

3. 在表单中添加选项:
```html
<option value="new-artist">New Artist</option>
```

### 添加新分类

1. 创建分类目录和数据文件
2. 脚本会自动检测并处理
3. 在表单中添加分类选项

## 🎯 使用方法

### 开发环境

```bash
# 启动开发服务器
npm run dev

# 提取专辑数据
npm run extract-albums
```

### 生产环境

```bash
# 构建 (自动运行专辑提取)
npm run build
```

## 🔧 配置选项

### 脚本配置

在 `scripts/extract-albums.js` 中:
```javascript
const ARTISTS = ['ye', 'playboi-carti']; // 支持的艺术家
const DATA_DIR = path.join(projectRoot, 'public', 'data', 'artists');
```

### 表单配置

在 `SimpleSubmissionForm.astro` 中:
```astro
const supportedLanguages = ['en', 'ar', 'pt'];
```

## 🎵 总结

这个解决方案提供了：

- **完整的多语言支持** - 所有表单文本都支持国际化
- **智能专辑选择** - 根据上下文动态加载相关专辑
- **自动化数据管理** - 脚本自动维护专辑列表
- **高度可扩展** - 易于添加新语言、艺术家和分类
- **性能优化** - 按需加载，减少初始加载时间

现在您可以在 `http://localhost:4322/` 查看更新后的表单，它将根据当前语言显示相应的提示信息，并支持动态专辑选择！
